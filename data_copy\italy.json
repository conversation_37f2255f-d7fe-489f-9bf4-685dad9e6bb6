{"italyExpenseReimbursementDatabaseTables": {"fileRelatedRequirements": [{"fieldType": "Customer Name on Invoice", "description": "Local Employer name as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "Global People s.r.l.", "mandatoryOptional": "Mandatory", "rule": "Must show Global People s.r.l. as customer"}, {"fieldType": "Customer Name on Invoice", "description": "Local Employer name as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "Must show GoGlobal Consulting S.r.l as customer"}, {"fieldType": "Customer Name Exception", "description": "Exception for flights/hotels where Local Employer name not possible", "receiptType": "Travel", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Optional", "rule": "Worker's name acceptable when Local Employer name not possible, end client should not be mentioned"}, {"fieldType": "Customer Address on Invoice", "description": "Local Employer address as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "Global People s.r.l.", "mandatoryOptional": "Mandatory", "rule": "Must show Via Venti Settembre 3, Torino (TO) CAP 10121, Italy"}, {"fieldType": "Customer Address on Invoice", "description": "Local Employer address as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "Must show <PERSON> 38, 20122 Milano, Italia"}, {"fieldType": "Customer VAT Number on Invoice", "description": "Local Employer VAT number as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "Global People s.r.l.", "mandatoryOptional": "Mandatory", "rule": "Must show *************"}, {"fieldType": "Customer VAT Number on Invoice", "description": "Local Employer VAT number as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "Must show P.IVA 12205930964"}, {"fieldType": "Customer Tax Code on Invoice", "description": "Local Employer tax code as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "Global People s.r.l.", "mandatoryOptional": "Mandatory", "rule": "Must show 12455930011"}, {"fieldType": "Customer Tax Code on Invoice", "description": "Local Employer tax code as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "GoGlobal Consulting S.r.l", "mandatoryOptional": "Optional", "rule": "Not specified in document"}, {"fieldType": "<PERSON><PERSON><PERSON><PERSON>", "description": "Receipt currency", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Same currency with clear exchange rate"}, {"fieldType": "Amount", "description": "Expense amount", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Must be clearly stated on receipt"}, {"fieldType": "Receipt Type", "description": "Type of supporting document", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Must be actual tax receipts or invoices, not booking confirmations"}, {"fieldType": "Receipt Quality", "description": "Document quality standard", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Must be scanned (not photos), clear and readable"}, {"fieldType": "Payment Method", "description": "Method of payment used", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Must be traceable: bank transfers, postal transfers, credit/debit cards, prepaid cards, bank/cashier's checks"}, {"fieldType": "Vehicle Make/Model", "description": "Car details for mileage claims", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Type of car, petrol/electric/hybrid, model"}, {"fieldType": "Vehicle Fuel Type", "description": "Fuel type for mileage claims", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Petrol, diesel, electric, hybrid"}, {"fieldType": "Distance Traveled", "description": "Kilometers for mileage claims", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Starting point, arrival point, and total kilometers"}, {"fieldType": "Route Documentation", "description": "Proof of distance traveled", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Screenshot from tracking app (Google Maps) or similar"}, {"fieldType": "Car Registration", "description": "Vehicle ownership proof", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Scan of car passport (libretto di circolazione)"}, {"fieldType": "Personal Information", "description": "Privacy requirement for receipts", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Any personal information not required for reimbursement must be removed"}, {"fieldType": "Business Trip Reporting", "description": "Separate reports requirement", "receiptType": "Travel", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Submit separate report for each business trip"}, {"fieldType": "Per <PERSON> Method", "description": "Method consistency rule", "receiptType": "Travel", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Cannot mix per diem method with actual expenses - must agree on one method per business trip"}], "complianceAndPolicies": [{"type": "Business Expenses (Non-Travel)", "description": "General business expenses for job completion", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit specified", "grossUpRule": "All approved expenses paid as NET to employee and grossed up", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide a proper tax invoice with VAT details"}, {"type": "Entertainment Expenses", "description": "Meals offered to client/supplier", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "75% tax-free", "grossUpRule": "Tax-free up to 75% of total amount, labeled as \"spese di rappresentanza\"", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must also write down the name and company details of the client/supplier you entertained"}, {"type": "Employee Engagement", "description": "Employee engagement activities", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit specified", "grossUpRule": "Grossed up if not tax-free", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - no additional information needed"}, {"type": "Training and Development", "description": "Training and development expenses", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit specified", "grossUpRule": "Grossed up if not tax-free", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - no additional information needed"}, {"type": "Car Rental", "description": "Vehicle rental for business", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "15 days limit", "grossUpRule": "Tax-free up to 15 days, subject to tax beyond 15 days", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - no additional information needed"}, {"type": "Mileage", "description": "Private vehicle use for work", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "15,000 km annually", "grossUpRule": "Non-taxable up to 15,000 km, excess subject to tax", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt is not applicable - you must provide: 1) Scan of car registration document 2) Car details (make/model/fuel type) 3) Google Maps screenshot showing route and distance"}, {"type": "Vehicle Expenses", "description": "Fuel, parking, tolls", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "20% tax-exempt", "grossUpRule": "Tax-exempt up to 20% of costs (rental cars only)", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - no additional information needed"}, {"type": "Transportation", "description": "Public transportation", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit", "grossUpRule": "100% tax-exempt with proper documentation", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide the actual ticket or transportation document (not just payment receipt)"}, {"type": "Parking Fees", "description": "Parking during business travel", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit", "grossUpRule": "Excluded from per diem, reimbursed against receipts", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - no additional information needed"}, {"type": "Fuel Expenses", "description": "Fuel costs", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Taxable", "grossUpRule": "Fuel expenses are taxed", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - no additional information needed"}, {"type": "Domestic Business Travel", "description": "Travel 60km+ outside municipal area", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Per diem rates apply", "grossUpRule": "€46.48/day (no provisions), €30.99/day (meals OR hotel provided), €15.49/day (both provided)", "additionalInfoRequired": true, "additionalInfoDescription": "Hotel receipt alone is not enough - you must ALWAYS provide receipts for ALL trip expenses when per diem is used"}, {"type": "Municipal Area Travel", "description": "Travel within municipal area", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "75% tax exemption", "grossUpRule": "75% tax exemption on per diem rate", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide receipts for all expenses within your city area"}, {"type": "Domestic Business Travel (NCBA Commercio)", "description": "Business trips with no overnight stay", "icpSpecific": true, "icpName": "NCBA Commercio", "grossUpLimit": "Per diem reduced by 1/3", "grossUpRule": "Per diem reduced by 1/3 for day trips", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide receipts for ALL trip expenses (meals, transportation)"}, {"type": "Domestic Business Travel (Others)", "description": "Business trips with no overnight stay", "icpSpecific": true, "icpName": "All other NCBAs", "grossUpLimit": "Standard per diem", "grossUpRule": "Standard per diem rates apply", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide receipts for ALL trip expenses (meals, transportation)"}, {"type": "International Business Travel", "description": "International business trips", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Per diem rates apply", "grossUpRule": "€77.46/day (no provisions), €51.65/day (meals OR hotel provided), €25.82/day (both provided)", "additionalInfoRequired": true, "additionalInfoDescription": "Hotel receipt alone is not enough - you must ALWAYS provide receipts for ALL trip expenses when per diem is used"}, {"type": "International Long-term Travel", "description": "International trips over 1 month", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "10% reduction", "grossUpRule": "Allowance reduced by 10% for missions over 1 month", "additionalInfoRequired": true, "additionalInfoDescription": "Hotel receipt alone is not enough - you must ALWAYS provide receipts for ALL trip expenses when per diem is used"}, {"type": "International Business Travel (NCBA Commercio)", "description": "International trips with no overnight stay", "icpSpecific": true, "icpName": "NCBA Commercio", "grossUpLimit": "Per diem reduced by 1/3", "grossUpRule": "Per diem reduced by 1/3 for day trips", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide receipts for ALL trip expenses (meals, transportation)"}, {"type": "International Business Travel (Others)", "description": "International trips with no overnight stay", "icpSpecific": true, "icpName": "All other NCBAs", "grossUpLimit": "Standard per diem", "grossUpRule": "Standard per diem rates apply", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide receipts for ALL trip expenses (meals, transportation)"}, {"type": "Car Rental (Domestic Travel)", "description": "Car rental during domestic business travel", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "15 days limit", "grossUpRule": "Tax-free up to 15 days, subject to tax beyond 15 days", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - no additional information needed"}, {"type": "Car Rental (International Travel)", "description": "Car rental during international business travel", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "15 days limit", "grossUpRule": "Tax-free up to 15 days, subject to tax beyond 15 days", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - no additional information needed"}, {"type": "Items Outside Per Diem", "description": "International travel expenses not covered by per diem", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit specified", "grossUpRule": "Not subject to any set maximum to be considered tax free", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide supporting documents such as receipts"}, {"type": "Toll Charges", "description": "Highway tolls and road charges", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "20% tax-exempt", "grossUpRule": "Tax-exempt up to 20% of costs (rental cars only, not assigned to specific employee)", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - no additional information needed"}, {"type": "Company Car Expenses", "description": "Expenses for company-assigned vehicles", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Not applicable", "grossUpRule": "Transportation expenses rules do not apply to company cars", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - no additional information needed"}]}}